/* 移动端优化样式 */
@media (max-width: 768px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .form-control-sm {
        font-size: 0.8rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
}

/* 卡片样式增强 */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-responsive {
    border-radius: 6px;
    overflow: hidden;
}

/* 输入框样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* 渐变背景 */
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 图标样式 */
.fas, .far {
    width: 1.2em;
    text-align: center;
}

/* 结果卡片动画 */
#results-card {
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 数据表格优化 */
#data-table input {
    min-width: 80px;
}

#data-table .btn-sm {
    padding: 0.25rem 0.5rem;
}

/* 快速输入表单 */
#quick-input-form .form-control {
    margin-bottom: 0.5rem;
}

/* 实验步骤列表 */
.list-group-numbered {
    counter-reset: section;
}

.list-group-numbered .list-group-item {
    border: 1px solid rgba(0,0,0,.125);
    margin-bottom: 0.5rem;
    border-radius: 6px;
}

.list-group-numbered .list-group-item::before {
    counter-increment: section;
    content: counter(section);
    position: absolute;
    left: -2.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: #007bff;
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* 响应式字体大小 */
@media (max-width: 576px) {
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
    
    h4 {
        font-size: 1.1rem;
    }
    
    h5 {
        font-size: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    #quick-input-form,
    .card-header .btn {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table th {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus {
        background-color: #4a5568;
        border-color: #63b3ed;
        color: #e2e8f0;
    }
}

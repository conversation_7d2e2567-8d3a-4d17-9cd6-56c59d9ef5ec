@echo off
chcp 65001 >nul
title 光电倍增管实验指导系统

echo ================================================
echo 光电倍增管实验指导系统
echo PMT Experiment Guide System
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 安装依赖包
echo 正在检查并安装依赖包...
python -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 依赖包安装失败，请检查网络连接
    pause
    exit /b 1
)

echo.
echo 正在启动Flask应用...
echo 服务器地址: http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo ------------------------------------------------
echo.

REM 启动应用
python run.py

pause

version: '3.8'

services:
  pmt-experiment:
    build: .
    container_name: pmt-experiment-app
    ports:
      - "20050:20050"
    environment:
      - FLASK_ENV=production
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=20050
      - FLASK_DEBUG=False
    volumes:
      - ./data:/app/data  # 可选：持久化数据目录
    restart: unless-stopped
    networks:
      - pmt-network

networks:
  pmt-network:
    driver: bridge

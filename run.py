#!/usr/bin/env python3
"""
光电倍增管实验指导系统启动脚本
PMT Experiment Guide System Launcher
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = "venv"
    if not os.path.exists(venv_path):
        print("正在创建虚拟环境...")
        try:
            subprocess.check_call([sys.executable, "-m", "venv", venv_path])
            print("虚拟环境创建完成!")
        except subprocess.CalledProcessError as e:
            print(f"虚拟环境创建失败: {e}")
            return False
    return True

def get_venv_python():
    """获取虚拟环境中的Python路径"""
    if os.name == 'nt':  # Windows
        return os.path.join("venv", "Scripts", "python.exe")
    else:  # Linux/Mac
        return os.path.join("venv", "bin", "python")

def install_requirements():
    """安装依赖包"""
    print("正在检查并安装依赖包...")

    # 首先尝试直接安装
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成!")
        return True, sys.executable
    except subprocess.CalledProcessError:
        print("系统环境受保护，正在创建虚拟环境...")

        # 创建虚拟环境
        if not create_virtual_environment():
            return False, None

        # 在虚拟环境中安装依赖
        venv_python = get_venv_python()
        try:
            subprocess.check_call([venv_python, "-m", "pip", "install", "-r", "requirements.txt"])
            print("依赖包安装完成!")
            return True, venv_python
        except subprocess.CalledProcessError as e:
            print(f"依赖包安装失败: {e}")
            return False, None

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待Flask启动
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("=" * 50)
    print("光电倍增管实验指导系统")
    print("PMT Experiment Guide System")
    print("=" * 50)

    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return

    python_executable = sys.executable

    # 检查是否存在requirements.txt
    if os.path.exists('requirements.txt'):
        # 询问是否安装依赖
        install = input("是否安装/更新依赖包? (y/n, 默认y): ").lower()
        if install != 'n':
            success, python_exec = install_requirements()
            if not success:
                input("按回车键退出...")
                return
            python_executable = python_exec

    print("\n正在启动Flask应用...")
    print("服务器地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)

    # 延迟打开浏览器
    Timer(2.0, open_browser).start()

    try:
        # 如果使用虚拟环境，需要启动子进程
        if python_executable != sys.executable:
            print("使用虚拟环境启动应用...")
            subprocess.run([python_executable, "app.py"])
        else:
            # 导入并运行Flask应用
            from app import app
            app.run(debug=True, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖包已正确安装")
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
    except Exception as e:
        print(f"启动错误: {e}")

    input("按回车键退出...")

if __name__ == "__main__":
    main()

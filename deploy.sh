#!/bin/bash

# 光电倍增管实验指导系统 Docker 部署脚本
# PMT Experiment Guide System Docker Deployment Script

echo "================================================"
echo "光电倍增管实验指导系统 Docker 部署"
echo "PMT Experiment Guide System Docker Deployment"
echo "================================================"
echo

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    echo "安装指南: https://docs.docker.com/get-docker/"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    echo "安装指南: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker环境检查通过"
echo

# 停止并删除现有容器
echo "🔄 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "❌ 镜像构建失败"
    exit 1
fi

echo "✅ 镜像构建成功"
echo

# 启动容器
echo "🚀 启动应用容器..."
docker-compose up -d

if [ $? -ne 0 ]; then
    echo "❌ 容器启动失败"
    exit 1
fi

echo "✅ 容器启动成功"
echo

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 5

# 检查容器状态
echo "📊 检查容器状态..."
docker-compose ps

# 检查应用是否可访问
echo
echo "🔍 测试应用连接..."
if curl -f http://localhost:20050/ >/dev/null 2>&1; then
    echo "✅ 应用启动成功！"
    echo
    echo "🌐 访问地址:"
    echo "   本地访问: http://localhost:20050"
    echo "   局域网访问: http://$(hostname -I | awk '{print $1}'):20050"
    echo
    echo "📋 管理命令:"
    echo "   查看日志: docker-compose logs -f"
    echo "   停止应用: docker-compose down"
    echo "   重启应用: docker-compose restart"
    echo
else
    echo "❌ 应用启动失败，请检查日志:"
    echo "   docker-compose logs"
fi

echo "================================================"
